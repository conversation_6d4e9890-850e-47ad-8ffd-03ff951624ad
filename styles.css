* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: white;
    border-bottom: 1px solid #e0e0e0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: bold;
    color: #00BCD4;
}

.logo img {
    width: 32px;
    height: 32px;
}

.search-bar {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    flex: 1;
    max-width: 400px;
    margin: 0 2rem;
}

.search-input {
    border: none;
    background: transparent;
    outline: none;
    flex: 1;
    padding: 0.25rem;
}

.search-btn {
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1.1rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification, .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 80px);
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: #f8f9fa;
    padding: 1rem;
    border-right: 1px solid #e0e0e0;
}

.nav-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background: #e9ecef;
}

.categories {
    margin-top: 2rem;
}

.categories h3 {
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.category-item {
    padding: 0.5rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.category-item:hover {
    background: #e9ecef;
}

.category-item.active {
    background: #00BCD4;
    color: white;
}

/* 产品区域 */
.product-area {
    flex: 1;
    padding: 1rem 2rem;
}

.tabs {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.tab {
    padding: 0.75rem 0;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: border-color 0.2s;
}

.tab.active {
    border-bottom-color: #00BCD4;
    color: #00BCD4;
    font-weight: 500;
}

.product-count {
    margin-bottom: 1rem;
    color: #666;
    font-size: 0.9rem;
}

/* 产品列表 */
.product-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: box-shadow 0.2s;
}

.product-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.rating {
    color: #ffc107;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s;
}

.btn-inquiry {
    background: #007bff;
    color: white;
}

.btn-register {
    background: #ffc107;
    color: #333;
}

.btn-sample {
    background: #28a745;
    color: white;
}

.btn-request {
    background: #17a2b8;
    color: white;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h2 {
    color: #333;
    font-size: 1.3rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 2rem;
}

/* 表单样式 */
.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.btn-attachment {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    color: #007bff;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-submit {
    background: #00BCD4;
    color: white;
}

/* 确认对话框样式 */
.confirm-modal {
    max-width: 400px;
    text-align: center;
}

.confirm-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-no {
    background: #6c757d;
    color: white;
    padding: 0.75rem 2rem;
}

.btn-yes {
    background: #28a745;
    color: white;
    padding: 0.75rem 2rem;
}

/* 完成提示样式 */
.done-modal {
    max-width: 300px;
    text-align: center;
}

.done-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.done-modal h2 {
    color: #28a745;
    margin-bottom: 1rem;
}

.btn-ok {
    background: #00BCD4;
    color: white;
    padding: 0.75rem 2rem;
    margin-top: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
    }
    
    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .product-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}
