* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
}

/* 头部样式 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 2rem;
    background: white;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #00BCD4;
}

.logo img {
    width: 28px;
    height: 28px;
}

.search-bar {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 24px;
    padding: 0.5rem 1.25rem;
    flex: 1;
    max-width: 450px;
    margin: 0 2rem;
    transition: all 0.2s ease;
}

.search-bar:focus-within {
    border-color: #00BCD4;
    box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.1);
}

.search-input {
    border: none;
    background: transparent;
    outline: none;
    flex: 1;
    padding: 0.25rem;
    font-size: 0.95rem;
    color: #495057;
}

.search-input::placeholder {
    color: #6c757d;
}

.search-btn {
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1.1rem;
    color: #6c757d;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.search-btn:hover {
    background: #e9ecef;
    color: #00BCD4;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.notification, .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.1rem;
}

.notification:hover, .user-avatar:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 90px);
    background: #f8f9fa;
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background: white;
    padding: 1.5rem 1rem;
    border-right: 1px solid #e9ecef;
    box-shadow: 2px 0 4px rgba(0,0,0,0.02);
}

.nav-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item:hover {
    background: #f8f9fa;
    color: #00BCD4;
    transform: translateX(2px);
}

.categories {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.categories h3 {
    margin-bottom: 1rem;
    color: #6c757d;
    font-size: 0.85rem;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.category-item {
    padding: 0.6rem 1rem;
    margin-bottom: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    color: #495057;
    position: relative;
}

.category-item:hover {
    background: #f8f9fa;
    color: #00BCD4;
    transform: translateX(2px);
}

.category-item.active {
    background: linear-gradient(135deg, #00BCD4, #00ACC1);
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 188, 212, 0.3);
}

.category-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: rgba(255,255,255,0.8);
    border-radius: 0 2px 2px 0;
}

/* 产品区域 */
.product-area {
    flex: 1;
    padding: 1.5rem 2rem;
    background: white;
    margin: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.04);
}

.tabs {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0;
}

.tab {
    padding: 1rem 0;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    font-weight: 500;
    color: #6c757d;
    position: relative;
}

.tab:hover {
    color: #00BCD4;
}

.tab.active {
    border-bottom-color: #00BCD4;
    color: #00BCD4;
    font-weight: 600;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00BCD4, #00ACC1);
    border-radius: 2px 2px 0 0;
}

.product-count {
    margin-bottom: 1.5rem;
    color: #00BCD4;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, rgba(0, 188, 212, 0.1), rgba(0, 172, 193, 0.1));
    border-radius: 20px;
    display: inline-block;
    border: 1px solid rgba(0, 188, 212, 0.2);
}

/* 产品列表 */
.product-list {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #00BCD4, #00ACC1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-item:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transform: translateY(-2px);
    border-color: rgba(0, 188, 212, 0.2);
}

.product-item:hover::before {
    opacity: 1;
}

.product-info {
    flex: 1;
}

.product-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.75rem;
    color: #212529;
    font-weight: 600;
}

.product-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem 1.5rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.product-details span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-details .category::before { content: '🏷️'; }
.product-details .formulation::before { content: '⚗️'; }
.product-details .concentration::before { content: '📊'; }
.product-details .supplier::before { content: '🏢'; }

.rating {
    color: #ffc107;
    font-size: 1rem;
}

.product-actions {
    display: flex;
    gap: 0.75rem;
    flex-shrink: 0;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-library {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-inquiry {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.btn-register {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
    color: white;
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-sample {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
    transition: transform 0.1s;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 3% auto;
    padding: 0;
    border-radius: 16px;
    width: 85%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    border: 1px solid rgba(255,255,255,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2.5rem;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border-radius: 16px 16px 0 0;
    position: relative;
}

/* 表单类型选择器 */
.form-type-selector {
    display: flex;
    gap: 0.5rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.form-type-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    background: white;
    color: #6c757d;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-type-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
}

.form-type-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.modal-header h2 {
    color: #212529;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.modal-body {
    padding: 2.5rem;
}

/* 表单样式 */
.form-section {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.form-section h3 {
    margin-bottom: 1.5rem;
    color: #212529;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #00BCD4;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00BCD4;
    box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
}

.btn-attachment {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #00BCD4;
    border-radius: 8px;
    cursor: pointer;
    color: #00BCD4;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-attachment:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    border-color: #00ACC1;
    transform: translateY(-1px);
}

.btn-attachment::before {
    content: '📎';
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.btn-cancel {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    padding: 0.75rem 2rem;
}

.btn-submit {
    background: linear-gradient(135deg, #00BCD4, #00ACC1);
    color: white;
    padding: 0.75rem 2rem;
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
}

/* 表单类型切换 */
.form-type {
    display: block;
}

/* 表单头部 */
.form-header {
    margin-bottom: 2rem;
}

.form-company-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border-left: 4px solid #007bff;
}

.form-company-info h3 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.company-rating {
    font-size: 1.2rem;
}

/* 表单网格布局 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section h4 {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* 全宽表单组 */
.form-group.full-width {
    grid-column: 1 / -1;
}

/* 复选框网格 */
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.75rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.checkbox-item:hover {
    background: #e9ecef;
}

.checkbox-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* 单选框样式 */
.radio-group {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.75rem;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
    margin-bottom: 0;
}

.radio-group input[type="radio"] {
    width: auto;
    margin: 0;
    padding: 0;
}

/* 带单位的输入框 */
.input-with-unit {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.unit-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 预测网格 */
.forecast-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 0.75rem;
}

.forecast-grid input {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.forecast-grid input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 样品详情 */
.sample-details input {
    width: 100%;
    margin-bottom: 0.5rem;
}

/* 表单组内的多个输入框 */
.form-group input + input {
    margin-top: 0.75rem;
}

/* 按钮样式优化 */
.btn-attachment {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-attachment:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* 确认对话框样式 */
.confirm-modal {
    max-width: 450px;
    text-align: center;
}

.confirm-modal .modal-body {
    padding: 3rem 2rem;
}

.confirm-modal p {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.confirm-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.btn-no {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 10px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-yes {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 10px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* 完成提示样式 */
.done-modal {
    max-width: 400px;
    text-align: center;
}

.done-modal .modal-body {
    padding: 3rem 2rem;
}

.done-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    animation: bounceIn 0.6s ease-out;
}

.done-modal h2 {
    color: #28a745;
    margin-bottom: 1rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.done-modal p {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 2rem;
}

.btn-ok {
    background: linear-gradient(135deg, #00BCD4, #00ACC1);
    color: white;
    padding: 1rem 3rem;
    border-radius: 10px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #00BCD4, #00ACC1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #00ACC1, #0097A7);
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-item {
    animation: fadeInUp 0.6s ease-out;
}

.product-item:nth-child(1) { animation-delay: 0.1s; }
.product-item:nth-child(2) { animation-delay: 0.2s; }
.product-item:nth-child(3) { animation-delay: 0.3s; }

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
    }

    .product-area {
        margin: 0.5rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 0.5rem 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .search-bar {
        order: 3;
        width: 100%;
        margin: 0;
        max-width: none;
    }

    .main-content {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 1rem;
    }

    .categories {
        margin-top: 1rem;
        padding-top: 1rem;
    }

    .product-area {
        margin: 0;
        padding: 1rem;
        border-radius: 0;
    }

    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .product-details {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .product-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
        border-radius: 12px;
    }

    .modal-header {
        padding: 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-section {
        padding: 1rem;
    }

    .confirm-actions,
    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 1rem;
    }

    .tabs {
        gap: 1rem;
        overflow-x: auto;
        padding-bottom: 0.5rem;
    }

    .tab {
        white-space: nowrap;
        padding: 0.75rem 0;
        min-width: fit-content;
    }

    .product-actions {
        grid-template-columns: repeat(2, 1fr);
        display: grid;
        gap: 0.5rem;
    }
}
