// Global variables
let currentFormType = 'price'; // Default to price form

// Open form modal
function openForm(type) {
    currentFormType = type;
    console.log('Opening form with type:', type);
    const modal = document.getElementById('formModal');
    const modalTitle = document.getElementById('modalTitle');

    // 隐藏所有表单类型
    document.getElementById('priceForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'none';
    document.getElementById('sampleForm').style.display = 'none';

    // 根据按钮类型设置标题和显示对应表单
    const formConfig = {
        'price': {
            title: '💰 Get Price',
            formId: 'priceForm'
        },
        'register': {
            title: '📋 Register',
            formId: 'registerForm'
        },
        'sample': {
            title: '🧪 Request Sample',
            formId: 'sampleForm'
        }
    };

    const config = formConfig[type] || formConfig['price'];
    modalTitle.innerHTML = config.title;
    document.getElementById(config.formId).style.display = 'block';

    modal.style.display = 'block';

    // 添加动画效果
    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// 保存到库功能
function saveToLibrary() {
    // 显示加载状态
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '📚 Saving...';
    button.disabled = true;

    // 模拟保存过程
    setTimeout(() => {
        button.textContent = '✅ Saved';
        button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

        // 显示成功通知
        showNotification('Product saved to library successfully!', 'success');

        // 2秒后恢复原状
        setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '';
            button.disabled = false;
        }, 2000);
    }, 1000);
}

// Test submit function
function testSubmit() {
    console.log('=== TEST SUBMIT CLICKED ===');
    console.log('Current form type:', currentFormType);

    // Simulate form submission
    const form = document.getElementById('enquiryForm');
    if (form) {
        console.log('Form found, triggering submit event');
        const event = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(event);
    } else {
        console.error('Form not found!');
    }
}



// 关闭表单模态框
function closeForm() {
    const modal = document.getElementById('formModal');
    const modalContent = modal.querySelector('.modal-content');
    
    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';
    
    setTimeout(() => {
        modal.style.display = 'none';
        // Reset form
        document.getElementById('enquiryForm').reset();
    }, 200);
}

// Handle form submission
function handleFormSubmit(event) {
    event.preventDefault();

    console.log('=== FORM SUBMISSION STARTED ===');
    console.log('Form submitted, currentFormType:', currentFormType);

    // Get form data
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    console.log('Form data:', data);

    // Validate required fields based on form type
    let missingFields = [];

    // Determine current form type from visible form
    const priceForm = document.getElementById('priceForm');
    const registerForm = document.getElementById('registerForm');
    const sampleForm = document.getElementById('sampleForm');

    if (priceForm && priceForm.style.display !== 'none') {
        currentFormType = 'price';
    } else if (registerForm && registerForm.style.display !== 'none') {
        currentFormType = 'register';
    } else if (sampleForm && sampleForm.style.display !== 'none') {
        currentFormType = 'sample';
    } else {
        // Check which form is visible by default (no inline style)
        if (priceForm && !priceForm.style.display) {
            currentFormType = 'price';
        } else {
            currentFormType = 'price'; // fallback
        }
    }
    console.log('Determined form type:', currentFormType);

    switch(currentFormType) {
        case 'price':
            if (!data.quantity) missingFields.push('Quantity');
            if (!data.payment) missingFields.push('Payment Terms');
            if (!data.deadline) missingFields.push('Deadline');
            break;
        case 'register':
            if (!data.sampleRequired) missingFields.push('Sample Required');
            if (!data.approvalPeriod) missingFields.push('Approval Period');
            if (!data.exclusive) missingFields.push('Exclusive');
            break;
        case 'sample':
            if (!data.sampleLocation) missingFields.push('Location');
            if (!data.samplePurposeReq) missingFields.push('Purpose');
            if (!data.sampleShipping) missingFields.push('Shipping Method');
            if (!data.sampleDeadline) missingFields.push('Deadline');
            break;
        default:
            console.log('Unknown form type, skipping validation');
            break;
    }

    console.log('Missing fields:', missingFields);

    if (missingFields.length > 0) {
        alert(`Please fill in the following required fields:\n${missingFields.join('\n')}`);
        return;
    }

    console.log('Validation passed, closing form and showing confirmation');

    // Close form
    closeForm();

    // Show confirmation dialog
    setTimeout(() => {
        showConfirmModal();
    }, 300);
}

// Show confirmation dialog
function showConfirmModal() {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'block';

    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// Close confirmation dialog
function closeConfirmModal() {
    const modal = document.getElementById('confirmModal');
    const modalContent = modal.querySelector('.modal-content');

    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';

    setTimeout(() => {
        modal.style.display = 'none';
    }, 200);
}

// Handle recommendation confirmation
function handleRecommendation(accepted) {
    console.log('User choice:', accepted ? 'Accept recommendation' : 'Reject recommendation');

    // Close confirmation dialog
    closeConfirmModal();

    // Show completion prompt
    setTimeout(() => {
        showDoneModal();
    }, 300);
}

// Show completion prompt
function showDoneModal() {
    const modal = document.getElementById('doneModal');
    modal.style.display = 'block';

    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// Close completion prompt
function closeDoneModal() {
    const modal = document.getElementById('doneModal');
    const modalContent = modal.querySelector('.modal-content');

    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';

    setTimeout(() => {
        modal.style.display = 'none';
    }, 200);
}

// Page initialization after loading
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== DOM CONTENT LOADED ===');

    // Bind form submit event
    const form = document.getElementById('enquiryForm');
    console.log('Form element:', form);

    if (form) {
        form.addEventListener('submit', handleFormSubmit);
        console.log('Form submit event listener added successfully');

        // Test if the event listener is working
        form.addEventListener('submit', function(e) {
            console.log('BACKUP: Form submit event triggered');
        });
    } else {
        console.error('Form with id "enquiryForm" not found');
    }

    // Click outside modal to close
    window.addEventListener('click', function(event) {
        const formModal = document.getElementById('formModal');
        const confirmModal = document.getElementById('confirmModal');
        const doneModal = document.getElementById('doneModal');

        if (event.target === formModal) {
            closeForm();
        } else if (event.target === confirmModal) {
            closeConfirmModal();
        } else if (event.target === doneModal) {
            closeDoneModal();
        }
    });

    // ESC key to close modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeForm();
            closeConfirmModal();
            closeDoneModal();
        }
    });

    // Add modal animation styles
    const style = document.createElement('style');
    style.textContent = `
        .modal-content {
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.2s ease-out;
        }
        
        .modal {
            animation: fadeIn 0.2s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .product-item {
            transition: all 0.3s ease;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
        }
        
        .btn {
            transition: all 0.2s ease;
        }
        
        .btn:active {
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);
    
    // Add search functionality
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', function(event) {
        const searchTerm = event.target.value.toLowerCase();
        const productItems = document.querySelectorAll('.product-item');

        productItems.forEach(item => {
            const productName = item.querySelector('h3').textContent.toLowerCase();
            const productDetails = item.querySelector('.product-details').textContent.toLowerCase();

            if (productName.includes(searchTerm) || productDetails.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Add category filtering functionality
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove all active states
            categoryItems.forEach(cat => cat.classList.remove('active'));
            // Add current active state
            this.classList.add('active');

            const selectedCategory = this.textContent.toLowerCase();
            const productItems = document.querySelectorAll('.product-item');

            productItems.forEach(product => {
                if (selectedCategory === 'all') {
                    product.style.display = 'flex';
                } else {
                    const productCategory = product.querySelector('.category').textContent.toLowerCase();
                    if (productCategory.includes(selectedCategory)) {
                        product.style.display = 'flex';
                    } else {
                        product.style.display = 'none';
                    }
                }
            });
        });
    });

    // Add tab switching functionality
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Add file upload functionality
    const attachmentBtn = document.querySelector('.btn-attachment');
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '.pdf,.doc,.docx,.jpg,.png';

            input.addEventListener('change', function(event) {
                const files = Array.from(event.target.files);
                if (files.length > 0) {
                    const fileNames = files.map(file => file.name).join(', ');
                    attachmentBtn.textContent = `Selected: ${fileNames}`;
                    attachmentBtn.style.color = '#28a745';
                }
            });

            input.click();
        });
    }

    // Add form validation
    const inputs = document.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#28a745';
            }
        });

        input.addEventListener('input', function() {
            if (this.value.trim()) {
                this.style.borderColor = '#28a745';
            }
        });
    });
});

// Utility function: Format date
function formatDate(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    };
    return new Date(date).toLocaleDateString('en-US', options);
}

// Utility function: Show loading state
function showLoading(button) {
    const originalText = button.textContent;
    button.textContent = 'Processing...';
    button.disabled = true;

    return function hideLoading() {
        button.textContent = originalText;
        button.disabled = false;
    };
}

// Utility function: Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        border-radius: 4px;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add animation styles
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);
