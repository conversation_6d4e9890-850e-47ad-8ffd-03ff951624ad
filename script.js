// 全局变量
let currentFormType = '';

// 打开表单模态框
function openForm(type) {
    currentFormType = type;
    const modal = document.getElementById('formModal');
    const modalTitle = document.getElementById('modalTitle');

    // 隐藏所有表单类型
    document.getElementById('inquiryForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'none';
    document.getElementById('sampleForm').style.display = 'none';

    // 根据按钮类型设置标题和显示对应表单
    const formConfig = {
        'inquiry': {
            title: '🟡 Create Enquiry',
            formId: 'inquiryForm'
        },
        'register': {
            title: '🟡 New Registration',
            formId: 'registerForm'
        },
        'sample': {
            title: '🟡 Create Sample Request',
            formId: 'sampleForm'
        },
        'request': {
            title: '🟡 Create Request',
            formId: 'inquiryForm'
        }
    };

    const config = formConfig[type] || formConfig['inquiry'];
    modalTitle.innerHTML = config.title;
    document.getElementById(config.formId).style.display = 'block';

    modal.style.display = 'block';

    // 添加动画效果
    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// 切换表单类型
function switchFormType(type) {
    currentFormType = type;

    // 更新按钮状态
    const buttons = document.querySelectorAll('.form-type-btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // 隐藏所有表单
    document.getElementById('inquiryForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'none';
    document.getElementById('sampleForm').style.display = 'none';

    // 显示对应表单和更新标题
    const formConfig = {
        'inquiry': {
            title: '🟡 Create Enquiry',
            formId: 'inquiryForm'
        },
        'register': {
            title: '🟡 New Registration',
            formId: 'registerForm'
        },
        'sample': {
            title: '🟡 Create Sample Request',
            formId: 'sampleForm'
        }
    };

    const config = formConfig[type];
    document.getElementById('modalTitle').innerHTML = config.title;
    document.getElementById(config.formId).style.display = 'block';
}

// 关闭表单模态框
function closeForm() {
    const modal = document.getElementById('formModal');
    const modalContent = modal.querySelector('.modal-content');
    
    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';
    
    setTimeout(() => {
        modal.style.display = 'none';
        // 重置表单
        document.getElementById('enquiryForm').reset();
    }, 200);
}

// 处理表单提交
function handleFormSubmit(event) {
    event.preventDefault();

    // 获取表单数据
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // 根据表单类型验证必填字段
    let isValid = true;
    let missingFields = [];

    switch(currentFormType) {
        case 'inquiry':
            if (!data.quantity) missingFields.push('Quantity');
            if (!data.payment) missingFields.push('Payment');
            if (!data.deadline) missingFields.push('Deadline');
            break;
        case 'register':
            if (!data.sampleRequired) missingFields.push('Sample Required');
            if (!data.approvalPeriod) missingFields.push('Approval Period');
            if (!data.exclusive) missingFields.push('Exclusive');
            break;
        case 'sample':
            if (!data.sampleLocation) missingFields.push('Location');
            if (!data.samplePurposeReq) missingFields.push('Purpose');
            if (!data.sampleShipping) missingFields.push('Shipping');
            if (!data.sampleDeadline) missingFields.push('Deadline');
            break;
    }

    if (missingFields.length > 0) {
        alert(`请填写以下必填字段：\n${missingFields.join('\n')}`);
        return;
    }

    // 关闭表单
    closeForm();

    // 显示确认对话框
    setTimeout(() => {
        showConfirmModal();
    }, 300);
}

// 显示确认对话框
function showConfirmModal() {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'block';
    
    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// 关闭确认对话框
function closeConfirmModal() {
    const modal = document.getElementById('confirmModal');
    const modalContent = modal.querySelector('.modal-content');
    
    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';
    
    setTimeout(() => {
        modal.style.display = 'none';
    }, 200);
}

// 处理推荐确认
function handleRecommendation(accepted) {
    console.log('用户选择:', accepted ? '接受推荐' : '拒绝推荐');
    
    // 关闭确认对话框
    closeConfirmModal();
    
    // 显示完成提示
    setTimeout(() => {
        showDoneModal();
    }, 300);
}

// 显示完成提示
function showDoneModal() {
    const modal = document.getElementById('doneModal');
    modal.style.display = 'block';
    
    setTimeout(() => {
        modal.querySelector('.modal-content').style.transform = 'scale(1)';
        modal.querySelector('.modal-content').style.opacity = '1';
    }, 10);
}

// 关闭完成提示
function closeDoneModal() {
    const modal = document.getElementById('doneModal');
    const modalContent = modal.querySelector('.modal-content');
    
    modalContent.style.transform = 'scale(0.9)';
    modalContent.style.opacity = '0';
    
    setTimeout(() => {
        modal.style.display = 'none';
    }, 200);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 绑定表单提交事件
    const form = document.getElementById('enquiryForm');
    form.addEventListener('submit', handleFormSubmit);
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        const formModal = document.getElementById('formModal');
        const confirmModal = document.getElementById('confirmModal');
        const doneModal = document.getElementById('doneModal');
        
        if (event.target === formModal) {
            closeForm();
        } else if (event.target === confirmModal) {
            closeConfirmModal();
        } else if (event.target === doneModal) {
            closeDoneModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeForm();
            closeConfirmModal();
            closeDoneModal();
        }
    });
    
    // 添加模态框动画样式
    const style = document.createElement('style');
    style.textContent = `
        .modal-content {
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.2s ease-out;
        }
        
        .modal {
            animation: fadeIn 0.2s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .product-item {
            transition: all 0.3s ease;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
        }
        
        .btn {
            transition: all 0.2s ease;
        }
        
        .btn:active {
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);
    
    // 添加搜索功能
    const searchInput = document.querySelector('.search-input');
    searchInput.addEventListener('input', function(event) {
        const searchTerm = event.target.value.toLowerCase();
        const productItems = document.querySelectorAll('.product-item');
        
        productItems.forEach(item => {
            const productName = item.querySelector('h3').textContent.toLowerCase();
            const productDetails = item.querySelector('.product-details').textContent.toLowerCase();
            
            if (productName.includes(searchTerm) || productDetails.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // 添加分类筛选功能
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有活动状态
            categoryItems.forEach(cat => cat.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');
            
            const selectedCategory = this.textContent.toLowerCase();
            const productItems = document.querySelectorAll('.product-item');
            
            productItems.forEach(product => {
                if (selectedCategory === 'all') {
                    product.style.display = 'flex';
                } else {
                    const productCategory = product.querySelector('.category').textContent.toLowerCase();
                    if (productCategory.includes(selectedCategory)) {
                        product.style.display = 'flex';
                    } else {
                        product.style.display = 'none';
                    }
                }
            });
        });
    });
    
    // 添加标签页切换功能
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // 添加文件上传功能
    const attachmentBtn = document.querySelector('.btn-attachment');
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = '.pdf,.doc,.docx,.jpg,.png';
            
            input.addEventListener('change', function(event) {
                const files = Array.from(event.target.files);
                if (files.length > 0) {
                    const fileNames = files.map(file => file.name).join(', ');
                    attachmentBtn.textContent = `已选择: ${fileNames}`;
                    attachmentBtn.style.color = '#28a745';
                }
            });
            
            input.click();
        });
    }
    
    // 添加表单验证
    const inputs = document.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.style.borderColor = '#dc3545';
            } else {
                this.style.borderColor = '#28a745';
            }
        });
        
        input.addEventListener('input', function() {
            if (this.value.trim()) {
                this.style.borderColor = '#28a745';
            }
        });
    });
});

// 工具函数：格式化日期
function formatDate(date) {
    const options = { 
        year: 'numeric', 
        month: '2-digit', 
        day: '2-digit' 
    };
    return new Date(date).toLocaleDateString('zh-CN', options);
}

// 工具函数：显示加载状态
function showLoading(button) {
    const originalText = button.textContent;
    button.textContent = '处理中...';
    button.disabled = true;
    
    return function hideLoading() {
        button.textContent = originalText;
        button.disabled = false;
    };
}

// 工具函数：显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        border-radius: 4px;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 添加动画样式
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(animationStyle);
